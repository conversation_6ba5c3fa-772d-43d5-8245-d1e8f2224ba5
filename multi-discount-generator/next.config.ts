import type { NextConfig } from 'next'
// @ts-ignore
import withPWA from 'next-pwa'

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    // 处理Node.js模块在客户端的兼容性问题
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      }
    }
    return config
  },
  experimental: {
    serverComponentsExternalPackages: ['chinese-holidays']
  }
}

export default withPWA({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development',
})(nextConfig)
