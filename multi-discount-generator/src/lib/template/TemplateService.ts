import * as XLSX from 'xlsx'
import type { FormData, TimeSlot } from '@/types'

/**
 * 自定义模板服务
 * 支持灵活的Excel模板格式，可以根据模板内容自动生成活动配置
 */
export class TemplateService {
  
  /**
   * 标准模板列定义
   */
  static readonly TEMPLATE_COLUMNS = {
    PRODUCT_ID: '商品ID',
    ACTIVITY_TYPE: '活动类型',
    START_TIME: '开始时间',
    END_TIME: '结束时间',
    REGION: '活动区域',
    MIN_QUANTITY: '满几件',
    DISCOUNT: '折扣',
    REDUCTION_AMOUNT: '立减金额',
    PRIORITY: '优先级',
    REMARKS: '备注'
  }

  /**
   * 支持的模板格式
   */
  static readonly TEMPLATE_FORMATS = {
    STANDARD: 'standard', // 标准格式
    CUSTOM: 'custom',     // 自定义格式
    BATCH: 'batch'        // 批量格式
  }

  /**
   * 创建标准模板
   */
  static createStandardTemplate(): XLSX.WorkBook {
    const headers = [
      this.TEMPLATE_COLUMNS.PRODUCT_ID,
      this.TEMPLATE_COLUMNS.ACTIVITY_TYPE,
      this.TEMPLATE_COLUMNS.START_TIME,
      this.TEMPLATE_COLUMNS.END_TIME,
      this.TEMPLATE_COLUMNS.REGION,
      this.TEMPLATE_COLUMNS.MIN_QUANTITY,
      this.TEMPLATE_COLUMNS.DISCOUNT,
      this.TEMPLATE_COLUMNS.REDUCTION_AMOUNT,
      this.TEMPLATE_COLUMNS.PRIORITY,
      this.TEMPLATE_COLUMNS.REMARKS
    ]

    // 示例数据
    const exampleData = [
      'PROD001',
      '多件多折',
      '2024-01-01 00:00:00',
      '2024-01-01 23:59:59',
      '全国',
      '2',
      '8.0',
      '',
      '1',
      '示例商品活动'
    ]

    const worksheetData = [headers, exampleData]
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)

    // 设置列宽
    worksheet['!cols'] = [
      { wch: 15 }, // 商品ID
      { wch: 12 }, // 活动类型
      { wch: 20 }, // 开始时间
      { wch: 20 }, // 结束时间
      { wch: 15 }, // 活动区域
      { wch: 10 }, // 满几件
      { wch: 10 }, // 折扣
      { wch: 12 }, // 立减金额
      { wch: 10 }, // 优先级
      { wch: 20 }  // 备注
    ]

    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, '活动模板')

    return workbook
  }

  /**
   * 创建批量模板
   */
  static createBatchTemplate(): XLSX.WorkBook {
    const headers = [
      this.TEMPLATE_COLUMNS.PRODUCT_ID,
      '商品标题',
      this.TEMPLATE_COLUMNS.ACTIVITY_TYPE,
      '时间模式', // 连续/随机
      '开始日期',
      '结束日期',
      '每日开始时间',
      '每日结束时间',
      this.TEMPLATE_COLUMNS.REGION,
      this.TEMPLATE_COLUMNS.MIN_QUANTITY,
      this.TEMPLATE_COLUMNS.DISCOUNT,
      '周末全天',
      '节假日全天',
      this.TEMPLATE_COLUMNS.REMARKS
    ]

    // 示例数据
    const exampleData = [
      'PROD001',
      '示例商品',
      '多件多折',
      '连续',
      '2024-01-01',
      '2024-01-07',
      '09:00:00',
      '21:00:00',
      '全国',
      '2',
      '8.0',
      '是',
      '是',
      '批量生成示例'
    ]

    const worksheetData = [headers, exampleData]
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)

    // 设置列宽
    worksheet['!cols'] = headers.map(() => ({ wch: 15 }))

    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, '批量模板')

    return workbook
  }

  /**
   * 解析模板文件
   */
  static async parseTemplate(file: File): Promise<{
    format: string
    data: any[]
    columns: string[]
    errors: string[]
  }> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const fileData = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(fileData, { type: 'array' })
          
          // 读取第一个工作表
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          
          // 转换为JSON数据
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          
          if (jsonData.length === 0) {
            resolve({
              format: 'unknown',
              data: [],
              columns: [],
              errors: ['模板文件为空']
            })
            return
          }

          const headers = jsonData[0] as string[]
          const rows = jsonData.slice(1) as any[][]
          
          // 检测模板格式
          const format = this.detectTemplateFormat(headers)
          
          // 验证模板
          const errors = this.validateTemplate(headers, rows, format)
          
          // 转换数据格式
          const parsedData = rows.map(row => {
            const rowData: any = {}
            headers.forEach((header, index) => {
              rowData[header] = row[index] || ''
            })
            return rowData
          })

          resolve({
            format,
            data: parsedData,
            columns: headers,
            errors
          })
        } catch (error) {
          reject(new Error('模板文件解析失败'))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 检测模板格式
   */
  private static detectTemplateFormat(headers: string[]): string {
    const standardColumns = Object.values(this.TEMPLATE_COLUMNS)
    const matchCount = headers.filter(header => standardColumns.includes(header)).length
    
    if (matchCount >= 6) {
      return this.TEMPLATE_FORMATS.STANDARD
    } else if (headers.includes('时间模式') || headers.includes('开始日期')) {
      return this.TEMPLATE_FORMATS.BATCH
    } else {
      return this.TEMPLATE_FORMATS.CUSTOM
    }
  }

  /**
   * 验证模板
   */
  private static validateTemplate(headers: string[], rows: any[][], format: string): string[] {
    const errors: string[] = []

    // 检查必需列
    const requiredColumns = [this.TEMPLATE_COLUMNS.PRODUCT_ID]
    
    if (format === this.TEMPLATE_FORMATS.STANDARD) {
      requiredColumns.push(
        this.TEMPLATE_COLUMNS.START_TIME,
        this.TEMPLATE_COLUMNS.END_TIME
      )
    } else if (format === this.TEMPLATE_FORMATS.BATCH) {
      requiredColumns.push('开始日期', '结束日期')
    }

    for (const column of requiredColumns) {
      if (!headers.includes(column)) {
        errors.push(`缺少必需列: ${column}`)
      }
    }

    // 检查数据行
    if (rows.length === 0) {
      errors.push('模板中没有数据行')
    }

    // 验证商品ID
    const productIdIndex = headers.indexOf(this.TEMPLATE_COLUMNS.PRODUCT_ID)
    if (productIdIndex >= 0) {
      rows.forEach((row, index) => {
        const productId = row[productIdIndex]
        if (!productId || typeof productId !== 'string' || productId.trim() === '') {
          errors.push(`第${index + 2}行: 商品ID不能为空`)
        }
      })
    }

    return errors
  }

  /**
   * 根据模板生成活动配置
   */
  static generateConfigFromTemplate(templateData: any[], format: string): {
    products: string[]
    configs: any[]
    errors: string[]
  } {
    const products: string[] = []
    const configs: any[] = []
    const errors: string[] = []

    for (const row of templateData) {
      try {
        const productId = row[this.TEMPLATE_COLUMNS.PRODUCT_ID]
        if (!productId) continue

        products.push(productId)

        if (format === this.TEMPLATE_FORMATS.STANDARD) {
          // 标准格式：直接使用时间段
          configs.push({
            productId,
            activityType: row[this.TEMPLATE_COLUMNS.ACTIVITY_TYPE] || '多件多折',
            startTime: row[this.TEMPLATE_COLUMNS.START_TIME],
            endTime: row[this.TEMPLATE_COLUMNS.END_TIME],
            region: row[this.TEMPLATE_COLUMNS.REGION] || '全国',
            minQuantity: parseInt(row[this.TEMPLATE_COLUMNS.MIN_QUANTITY]) || 1,
            discount: parseFloat(row[this.TEMPLATE_COLUMNS.DISCOUNT]) || 8.0,
            reductionAmount: row[this.TEMPLATE_COLUMNS.REDUCTION_AMOUNT] || '',
            priority: parseInt(row[this.TEMPLATE_COLUMNS.PRIORITY]) || 1,
            remarks: row[this.TEMPLATE_COLUMNS.REMARKS] || ''
          })
        } else if (format === this.TEMPLATE_FORMATS.BATCH) {
          // 批量格式：需要生成时间段
          configs.push({
            productId,
            activityType: row[this.TEMPLATE_COLUMNS.ACTIVITY_TYPE] || '多件多折',
            timeMode: row['时间模式'] || '连续',
            startDate: row['开始日期'],
            endDate: row['结束日期'],
            dailyStartTime: row['每日开始时间'] || '09:00:00',
            dailyEndTime: row['每日结束时间'] || '21:00:00',
            region: row[this.TEMPLATE_COLUMNS.REGION] || '全国',
            minQuantity: parseInt(row[this.TEMPLATE_COLUMNS.MIN_QUANTITY]) || 1,
            discount: parseFloat(row[this.TEMPLATE_COLUMNS.DISCOUNT]) || 8.0,
            weekendFullDay: row['周末全天'] === '是',
            holidayFullDay: row['节假日全天'] === '是',
            remarks: row[this.TEMPLATE_COLUMNS.REMARKS] || ''
          })
        }
      } catch (error) {
        errors.push(`处理商品 ${row[this.TEMPLATE_COLUMNS.PRODUCT_ID]} 时出错: ${error.message}`)
      }
    }

    return {
      products: [...new Set(products)], // 去重
      configs,
      errors
    }
  }

  /**
   * 导出模板文件
   */
  static exportTemplate(format: string, filename?: string): void {
    let workbook: XLSX.WorkBook

    switch (format) {
      case this.TEMPLATE_FORMATS.STANDARD:
        workbook = this.createStandardTemplate()
        break
      case this.TEMPLATE_FORMATS.BATCH:
        workbook = this.createBatchTemplate()
        break
      default:
        workbook = this.createStandardTemplate()
    }

    const defaultFilename = `活动模板_${format}_${new Date().toISOString().slice(0, 10)}.xlsx`
    XLSX.writeFile(workbook, filename || defaultFilename)
  }
}
