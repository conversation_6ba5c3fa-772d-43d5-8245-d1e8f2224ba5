/**
 * 商品管理相关类型定义
 */

export interface ProductInfo {
  id: string
  productId: string
  title: string
  basePrice: number
  limitPrice?: number
  purchaseDiscount?: number
  activityType: ActivityType
  activityStartTime?: Date
  activityEndTime?: Date
  supplierId?: string
  createdAt: Date
  updatedAt: Date
  skus?: ProductSku[]
  supplier?: Supplier
}

export interface ProductSku {
  id: string
  skuId: string
  productId: string
  skuName?: string
  price?: number
  stock?: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Supplier {
  id: string
  name: string
  code: string
  contact?: string
  phone?: string
  email?: string
  address?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface ProductTemplate {
  id: string
  name: string
  description?: string
  templateData: string
  isDefault: boolean
  createdAt: Date
  updatedAt: Date
}

export enum ActivityType {
  DISCOUNT = 'DISCOUNT',
  DIRECT_REDUCTION = 'DIRECT_REDUCTION',
  INSTANT_REDUCTION = 'INSTANT_REDUCTION'
}

// 表单数据类型
export interface ProductFormData {
  productId: string
  title: string
  basePrice: number
  limitPrice?: number
  purchaseDiscount?: number
  activityType: ActivityType
  activityStartTime?: Date
  activityEndTime?: Date
  supplierId?: string
  skus: SkuFormData[]
}

export interface SkuFormData {
  skuId: string
  skuName?: string
  price?: number
  stock?: number
  isActive: boolean
}

export interface SupplierFormData {
  name: string
  code: string
  contact?: string
  phone?: string
  email?: string
  address?: string
  isActive: boolean
}

// API 响应类型
export interface ProductListResponse {
  products: ProductInfo[]
  total: number
  page: number
  pageSize: number
}

export interface ProductSearchParams {
  page?: number
  pageSize?: number
  search?: string
  activityType?: ActivityType
  supplierId?: string
  isActive?: boolean
}

// 批量操作类型
export interface BatchProductOperation {
  action: 'delete' | 'activate' | 'deactivate' | 'updateSupplier'
  productIds: string[]
  data?: any
}

// 导入导出类型
export interface ProductImportResult {
  success: boolean
  imported: number
  failed: number
  errors: string[]
  duplicates: string[]
}

export interface ProductExportParams {
  productIds?: string[]
  activityType?: ActivityType
  supplierId?: string
  format: 'excel' | 'csv'
  includeSkus: boolean
}

// 活动类型选项
export const ACTIVITY_TYPE_OPTIONS = [
  { value: ActivityType.DISCOUNT, label: '打折' },
  { value: ActivityType.DIRECT_REDUCTION, label: '直降' },
  { value: ActivityType.INSTANT_REDUCTION, label: '立减' }
]

// 表单验证规则
export const PRODUCT_VALIDATION_RULES = {
  productId: {
    required: true,
    pattern: /^[A-Za-z0-9_-]+$/,
    maxLength: 50
  },
  title: {
    required: true,
    maxLength: 200
  },
  basePrice: {
    required: true,
    min: 0,
    max: 999999.99
  },
  limitPrice: {
    min: 0,
    max: 999999.99
  },
  purchaseDiscount: {
    min: 0,
    max: 1
  },
  skuId: {
    required: true,
    pattern: /^[A-Za-z0-9_-]+$/,
    maxLength: 50
  }
}
