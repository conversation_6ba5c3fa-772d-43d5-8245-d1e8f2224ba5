import type { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import type { ProductFormData } from '@/types/product'
import type { ApiResponse } from '@/types/api'

/**
 * 创建商品信息API
 * POST /api/products/create
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<{ productId: string }>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const productData: ProductFormData = req.body

    // 验证必填字段
    if (!productData.productId || !productData.title || !productData.basePrice) {
      return res.status(400).json({
        success: false,
        error: '商品ID、标题和定价为必填字段'
      })
    }

    // 验证商品ID格式
    if (!/^[A-Za-z0-9_-]+$/.test(productData.productId)) {
      return res.status(400).json({
        success: false,
        error: '商品ID格式不正确，只能包含字母、数字、下划线和横线'
      })
    }

    // 检查商品ID是否已存在
    const existingProduct = await prisma.productInfo.findUnique({
      where: { productId: productData.productId }
    })

    if (existingProduct) {
      return res.status(400).json({
        success: false,
        error: '商品ID已存在'
      })
    }

    // 验证SKU数据
    if (!productData.skus || productData.skus.length === 0) {
      return res.status(400).json({
        success: false,
        error: '至少需要一个SKU'
      })
    }

    // 检查SKU ID重复
    const skuIds = productData.skus.map(sku => sku.skuId).filter(id => id.trim())
    const uniqueSkuIds = new Set(skuIds)
    if (skuIds.length !== uniqueSkuIds.size) {
      return res.status(400).json({
        success: false,
        error: 'SKU ID不能重复'
      })
    }

    // 检查SKU ID是否已存在
    const existingSkus = await prisma.productSku.findMany({
      where: {
        skuId: { in: skuIds }
      }
    })

    if (existingSkus.length > 0) {
      return res.status(400).json({
        success: false,
        error: `以下SKU ID已存在: ${existingSkus.map(sku => sku.skuId).join(', ')}`
      })
    }

    // 验证供应商ID（如果提供）
    if (productData.supplierId) {
      const supplier = await prisma.supplier.findUnique({
        where: { id: productData.supplierId }
      })

      if (!supplier) {
        return res.status(400).json({
          success: false,
          error: '指定的供应商不存在'
        })
      }
    }

    // 使用事务创建商品和SKU
    const result = await prisma.$transaction(async (tx) => {
      // 创建商品信息
      const product = await tx.productInfo.create({
        data: {
          productId: productData.productId,
          title: productData.title,
          basePrice: productData.basePrice,
          limitPrice: productData.limitPrice,
          purchaseDiscount: productData.purchaseDiscount,
          activityType: productData.activityType,
          activityStartTime: productData.activityStartTime,
          activityEndTime: productData.activityEndTime,
          supplierId: productData.supplierId
        }
      })

      // 创建SKU信息
      const skus = await Promise.all(
        productData.skus.map(skuData =>
          tx.productSku.create({
            data: {
              skuId: skuData.skuId,
              productId: product.productId,
              skuName: skuData.skuName,
              price: skuData.price,
              stock: skuData.stock,
              isActive: skuData.isActive
            }
          })
        )
      )

      return { product, skus }
    })

    return res.status(201).json({
      success: true,
      data: {
        productId: result.product.productId
      }
    })

  } catch (error) {
    console.error('创建商品失败:', error)
    
    // 处理Prisma错误
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        error: '商品ID或SKU ID已存在'
      })
    }

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '创建商品失败'
    })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb'
    }
  }
}
