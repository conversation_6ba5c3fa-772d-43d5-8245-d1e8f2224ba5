import type { NextApiRequest, NextApiResponse } from 'next'
import * as XLSX from 'xlsx'
import type { ApiResponse } from '@/types/api'
import { TemplateService } from '@/lib/template/TemplateService'

interface BatchExportRequest {
  productIds: string[]
  configs: {
    activityType: string
    timeMode: string
    startDate: string
    endDate: string
    dailyStartTime: string
    dailyEndTime: string
    region: string
    minQuantity: number
    discount: number
    weekendFullDay: boolean
    holidayFullDay: boolean
  }
}

/**
 * 批量导出活动时间表格API
 * POST /api/batch/export
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<any>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const { productIds, configs } = req.body as BatchExportRequest

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: '商品ID列表不能为空'
      })
    }

    // 生成活动时间表格数据
    const scheduleData = generateActivitySchedule(productIds, configs)

    // 创建Excel工作簿
    const workbook = XLSX.utils.book_new()

    // 创建活动时间表工作表
    const scheduleHeaders = [
      '商品ID',
      '活动类型', 
      '活动日期',
      '开始时间',
      '结束时间',
      '活动区域',
      '满几件',
      '折扣',
      '是否全天',
      '备注'
    ]

    const scheduleRows = scheduleData.map(item => [
      item.productId,
      item.activityType,
      item.date,
      item.startTime,
      item.endTime,
      item.region,
      item.minQuantity,
      item.discount,
      item.isFullDay ? '是' : '否',
      item.remarks || ''
    ])

    const scheduleWorksheetData = [scheduleHeaders, ...scheduleRows]
    const scheduleWorksheet = XLSX.utils.aoa_to_sheet(scheduleWorksheetData)

    // 设置列宽
    scheduleWorksheet['!cols'] = [
      { wch: 15 }, // 商品ID
      { wch: 12 }, // 活动类型
      { wch: 12 }, // 活动日期
      { wch: 10 }, // 开始时间
      { wch: 10 }, // 结束时间
      { wch: 10 }, // 活动区域
      { wch: 8 },  // 满几件
      { wch: 8 },  // 折扣
      { wch: 8 },  // 是否全天
      { wch: 20 }  // 备注
    ]

    XLSX.utils.book_append_sheet(workbook, scheduleWorksheet, '活动时间表')

    // 创建汇总统计工作表
    const summaryData = generateSummaryData(productIds, scheduleData)
    const summaryHeaders = ['统计项', '数值', '说明']
    const summaryRows = [
      ['商品总数', productIds.length, '参与活动的商品数量'],
      ['活动天数', summaryData.totalDays, '活动持续的天数'],
      ['活动时段数', scheduleData.length, '总的活动时段数量'],
      ['平均每日时段', Math.round(scheduleData.length / summaryData.totalDays * 100) / 100, '平均每天的活动时段数'],
      ['周末时段数', summaryData.weekendSlots, '周末的活动时段数'],
      ['工作日时段数', summaryData.weekdaySlots, '工作日的活动时段数']
    ]

    const summaryWorksheetData = [summaryHeaders, ...summaryRows]
    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryWorksheetData)

    summaryWorksheet['!cols'] = [
      { wch: 15 }, // 统计项
      { wch: 10 }, // 数值
      { wch: 25 }  // 说明
    ]

    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '统计汇总')

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
    const filename = `活动时间表_${timestamp}.xlsx`

    // 将工作簿转换为buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`)
    res.setHeader('Content-Length', buffer.length)

    // 发送文件
    res.send(buffer)

  } catch (error) {
    console.error('批量导出失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '批量导出失败'
    })
  }
}

/**
 * 生成活动时间表数据
 */
function generateActivitySchedule(productIds: string[], configs: any) {
  const scheduleData: any[] = []
  
  const startDate = new Date(configs.startDate)
  const endDate = new Date(configs.endDate)
  
  // 遍历每个商品ID
  productIds.forEach(productId => {
    // 遍历日期范围
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const currentDate = new Date(date)
      const dateStr = currentDate.toISOString().slice(0, 10)
      const dayOfWeek = currentDate.getDay() // 0=周日, 6=周六
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6

      // 判断是否全天活动
      const isFullDay = (isWeekend && configs.weekendFullDay) || 
                       (isHoliday(currentDate) && configs.holidayFullDay)

      if (isFullDay) {
        // 全天活动
        scheduleData.push({
          productId,
          activityType: configs.activityType,
          date: dateStr,
          startTime: '00:00:00',
          endTime: '23:59:59',
          region: configs.region,
          minQuantity: configs.minQuantity,
          discount: configs.discount,
          isFullDay: true,
          remarks: isWeekend ? '周末全天' : '节假日全天'
        })
      } else {
        // 按设定时间段
        scheduleData.push({
          productId,
          activityType: configs.activityType,
          date: dateStr,
          startTime: configs.dailyStartTime,
          endTime: configs.dailyEndTime,
          region: configs.region,
          minQuantity: configs.minQuantity,
          discount: configs.discount,
          isFullDay: false,
          remarks: ''
        })
      }
    }
  })

  return scheduleData
}

/**
 * 生成汇总统计数据
 */
function generateSummaryData(productIds: string[], scheduleData: any[]) {
  const startDate = new Date(Math.min(...scheduleData.map(item => new Date(item.date).getTime())))
  const endDate = new Date(Math.max(...scheduleData.map(item => new Date(item.date).getTime())))
  
  const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
  
  const weekendSlots = scheduleData.filter(item => {
    const date = new Date(item.date)
    const dayOfWeek = date.getDay()
    return dayOfWeek === 0 || dayOfWeek === 6
  }).length

  const weekdaySlots = scheduleData.length - weekendSlots

  return {
    totalDays,
    weekendSlots,
    weekdaySlots
  }
}

/**
 * 判断是否为节假日（简单实现，可以根据需要扩展）
 */
function isHoliday(date: Date): boolean {
  // 这里可以添加节假日判断逻辑
  // 暂时返回false，可以根据实际需求添加节假日数据
  return false
}
