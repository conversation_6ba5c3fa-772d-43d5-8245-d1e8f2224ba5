'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { SimpleSelect as Select } from '@/components/ui/SimpleSelect'
import { Label } from '@/components/ui/Label'
import { Badge } from '@/components/ui/Badge'
import {
  Search,
  Filter,
  Download,
  FileText,
  Calendar,
  Package,
  CheckCircle,
  XCircle,
  Upload
} from 'lucide-react'
import { FileUpload } from './FileUpload'
import type { ProductInfo, ActivityType } from '@/types/product'

interface ExportFilters {
  productIds: string[]
  activityType?: ActivityType
  supplierId?: string
  dateRange?: {
    start: Date
    end: Date
  }
  format: 'excel' | 'csv'
  includeSkus: boolean
}

interface Supplier {
  id: string
  name: string
  code: string
}

export function BatchExport() {
  const [products, setProducts] = useState<ProductInfo[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [filters, setFilters] = useState<ExportFilters>({
    productIds: [],
    format: 'excel',
    includeSkus: true
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [templateData, setTemplateData] = useState<any>(null)
  const [showTemplateUpload, setShowTemplateUpload] = useState(false)
  const [batchConfigs, setBatchConfigs] = useState({
    activityType: '多件多折',
    timeMode: '连续',
    startDate: '',
    endDate: '',
    dailyStartTime: '09:00:00',
    dailyEndTime: '21:00:00',
    region: '全国',
    minQuantity: 1,
    discount: 8,
    weekendFullDay: false,
    holidayFullDay: false
  })

  // 加载数据
  useEffect(() => {
    loadProducts()
    loadSuppliers()
  }, [])

  const loadProducts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/products')
      const result = await response.json()
      
      if (result.success) {
        setProducts(result.data.products)
      }
    } catch (error) {
      console.error('加载商品失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSuppliers = async () => {
    try {
      const response = await fetch('/api/suppliers')
      const result = await response.json()
      
      if (result.success) {
        setSuppliers(result.data)
      }
    } catch (error) {
      console.error('加载供应商失败:', error)
    }
  }

  // 筛选商品
  const filteredProducts = products.filter(product => {
    const matchesSearch = searchTerm === '' || 
      product.productId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.title.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesActivityType = !filters.activityType || 
      product.activityType === filters.activityType
    
    const matchesSupplier = !filters.supplierId || 
      product.supplierId === filters.supplierId

    return matchesSearch && matchesActivityType && matchesSupplier
  })

  // 处理商品选择
  const handleProductSelect = (productId: string, selected: boolean) => {
    if (selected) {
      setSelectedProducts(prev => [...prev, productId])
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId))
    }
  }

  // 全选/取消全选
  const handleSelectAll = (selectAll: boolean) => {
    if (selectAll) {
      setSelectedProducts(filteredProducts.map(p => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  // 处理模板上传成功
  const handleTemplateUpload = (result: any) => {
    if (result.success) {
      setTemplateData(result)
      setSelectedProducts(result.products || [])
      setShowTemplateUpload(false)

      // 如果模板中有配置信息，更新批量配置
      if (result.configs && result.configs.length > 0) {
        const config = result.configs[0]
        setBatchConfigs(prev => ({
          ...prev,
          ...config
        }))
      }
    }
  }

  // 处理模板上传错误
  const handleTemplateUploadError = (error: string) => {
    alert('模板上传失败: ' + error)
  }

  // 处理批量导出
  const handleBatchExport = async () => {
    if (selectedProducts.length === 0) {
      alert('请选择要导出的商品或上传模板')
      return
    }

    if (!batchConfigs.startDate || !batchConfigs.endDate) {
      alert('请设置活动开始和结束日期')
      return
    }

    try {
      setExporting(true)

      const response = await fetch('/api/batch/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productIds: selectedProducts,
          configs: batchConfigs
        })
      })

      if (response.ok) {
        // 下载文件
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `活动时间表_${new Date().toISOString().slice(0, 10)}.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        throw new Error('导出失败')
      }
    } catch (error) {
      console.error('批量导出失败:', error)
      alert('批量导出失败')
    } finally {
      setExporting(false)
    }
  }

  // 处理导出
  const handleExport = async () => {
    if (selectedProducts.length === 0) {
      alert('请至少选择一个商品')
      return
    }

    try {
      setExporting(true)

      const exportData = {
        productIds: selectedProducts,
        activityType: filters.activityType,
        supplierId: filters.supplierId,
        format: filters.format,
        includeSkus: filters.includeSkus,
        dateRange: filters.dateRange
      }

      const response = await fetch('/api/export/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(exportData)
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `商品导出_${new Date().toISOString().slice(0, 10)}.${filters.format}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        throw new Error('导出失败')
      }
    } catch (error) {
      console.error('导出失败:', error)
      alert('导出失败: ' + error.message)
    } finally {
      setExporting(false)
    }
  }

  const getActivityTypeLabel = (type: ActivityType) => {
    const labels = {
      DISCOUNT: '打折',
      DIRECT_REDUCTION: '直降',
      INSTANT_REDUCTION: '立减'
    }
    return labels[type] || type
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">批量导出</h1>
        <p className="text-gray-600">上传Excel模板或手动选择商品，生成活动时间表格</p>
      </div>

      {/* 模板上传和批量配置 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 模板上传 */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Excel模板上传</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTemplateUpload(!showTemplateUpload)}
            >
              <Upload className="w-4 h-4 mr-2" />
              {showTemplateUpload ? '隐藏上传' : '上传模板'}
            </Button>
          </div>

          {showTemplateUpload && (
            <div className="space-y-4">
              <FileUpload
                onSuccess={handleTemplateUpload}
                onError={handleTemplateUploadError}
                loading={loading}
                accept=".xlsx,.xls,.csv"
                uploadUrl="/api/templates/upload"
              />
              <div className="text-sm text-gray-600">
                <p>• 支持Excel (.xlsx, .xls) 和CSV格式</p>
                <p>• 模板应包含：商品ID、活动类型、时间配置等字段</p>
                <p>• 上传后将自动解析商品ID并应用配置</p>
              </div>
            </div>
          )}

          {templateData && (
            <div className="mt-4 p-4 bg-green-50 rounded-lg">
              <div className="flex items-center text-green-800">
                <CheckCircle className="w-5 h-5 mr-2" />
                <span className="font-medium">模板上传成功</span>
              </div>
              <div className="mt-2 text-sm text-green-700">
                <p>解析到 {templateData.products?.length || 0} 个商品ID</p>
                <p>文件名: {templateData.originalName}</p>
              </div>
            </div>
          )}
        </Card>

        {/* 批量配置 */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">活动配置</h2>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">开始日期</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={batchConfigs.startDate}
                  onChange={(e) => setBatchConfigs(prev => ({ ...prev, startDate: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="endDate">结束日期</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={batchConfigs.endDate}
                  onChange={(e) => setBatchConfigs(prev => ({ ...prev, endDate: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="dailyStartTime">每日开始时间</Label>
                <Input
                  id="dailyStartTime"
                  type="time"
                  value={batchConfigs.dailyStartTime}
                  onChange={(e) => setBatchConfigs(prev => ({ ...prev, dailyStartTime: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="dailyEndTime">每日结束时间</Label>
                <Input
                  id="dailyEndTime"
                  type="time"
                  value={batchConfigs.dailyEndTime}
                  onChange={(e) => setBatchConfigs(prev => ({ ...prev, dailyEndTime: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="minQuantity">满几件</Label>
                <Input
                  id="minQuantity"
                  type="number"
                  min="1"
                  value={batchConfigs.minQuantity}
                  onChange={(e) => setBatchConfigs(prev => ({ ...prev, minQuantity: parseInt(e.target.value) || 1 }))}
                />
              </div>
              <div>
                <Label htmlFor="discount">折扣</Label>
                <Input
                  id="discount"
                  type="number"
                  min="1"
                  max="10"
                  value={batchConfigs.discount}
                  onChange={(e) => setBatchConfigs(prev => ({ ...prev, discount: parseInt(e.target.value) || 8 }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={batchConfigs.weekendFullDay}
                  onChange={(e) => setBatchConfigs(prev => ({ ...prev, weekendFullDay: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">周末全天活动</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={batchConfigs.holidayFullDay}
                  onChange={(e) => setBatchConfigs(prev => ({ ...prev, holidayFullDay: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">节假日全天活动</span>
              </label>
            </div>

            <Button
              onClick={handleBatchExport}
              disabled={exporting || selectedProducts.length === 0}
              className="w-full"
            >
              <Calendar className="w-4 h-4 mr-2" />
              {exporting ? '生成中...' : '生成活动时间表'}
            </Button>
          </div>
        </Card>
      </div>

      {/* 筛选条件 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <Label htmlFor="search">搜索商品</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                id="search"
                placeholder="商品ID或标题"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="activityType">活动类型</Label>
            <Select
              value={filters.activityType || ''}
              onValueChange={(value) => setFilters(prev => ({ 
                ...prev, 
                activityType: value as ActivityType || undefined 
              }))}
            >
              <option value="">全部类型</option>
              <option value="DISCOUNT">打折</option>
              <option value="DIRECT_REDUCTION">直降</option>
              <option value="INSTANT_REDUCTION">立减</option>
            </Select>
          </div>

          <div>
            <Label htmlFor="supplier">供应商</Label>
            <Select
              value={filters.supplierId || ''}
              onValueChange={(value) => setFilters(prev => ({ 
                ...prev, 
                supplierId: value || undefined 
              }))}
            >
              <option value="">全部供应商</option>
              {suppliers.map(supplier => (
                <option key={supplier.id} value={supplier.id}>
                  {supplier.name}
                </option>
              ))}
            </Select>
          </div>

          <div>
            <Label htmlFor="format">导出格式</Label>
            <Select
              value={filters.format}
              onValueChange={(value) => setFilters(prev => ({ 
                ...prev, 
                format: value as 'excel' | 'csv'
              }))}
            >
              <option value="excel">Excel (.xlsx)</option>
              <option value="csv">CSV (.csv)</option>
            </Select>
          </div>
        </div>

        <div className="mt-4 flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.includeSkus}
              onChange={(e) => setFilters(prev => ({ 
                ...prev, 
                includeSkus: e.target.checked 
              }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">包含SKU信息</span>
          </label>
        </div>
      </Card>

      {/* 商品列表 */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            商品列表 ({filteredProducts.length} 个商品)
          </h2>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">全选</span>
            </div>
            
            <Badge color="blue">
              已选择 {selectedProducts.length} 个
            </Badge>
            
            <Button
              onClick={handleExport}
              disabled={selectedProducts.length === 0 || exporting}
            >
              <Download className="w-4 h-4 mr-2" />
              {exporting ? '导出中...' : '导出选中'}
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">加载中...</span>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="text-center py-8">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到商品</h3>
            <p className="mt-1 text-sm text-gray-500">请调整筛选条件</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedProducts.length === filteredProducts.length}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    商品信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    活动类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    SKU数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    供应商
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedProducts.includes(product.id)}
                        onChange={(e) => handleProductSelect(product.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {product.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          ID: {product.productId}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge color="blue">
                        {getActivityTypeLabel(product.activityType)}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {product.skus?.length || 0} 个
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {product.supplier?.name || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(product.createdAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  )
}
