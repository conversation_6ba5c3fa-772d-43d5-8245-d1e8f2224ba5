'use client'

import { useState, useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useNotifications } from '@/hooks/useSocket'

interface AdminLayoutProps {
  children: ReactNode
}

interface NavItem {
  key: string
  label: string
  icon: string
  path: string
  description: string
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [uptime, setUptime] = useState(0)
  const [mounted, setMounted] = useState(false)
  const { notifications, clearNotifications } = useNotifications()

  // 避免水合失败 - 只在客户端显示动态内容
  useEffect(() => {
    setMounted(true)
    const startTime = Date.now()

    const updateUptime = () => {
      setUptime(Math.floor((Date.now() - startTime) / 1000 / 60))
    }

    updateUptime()
    const interval = setInterval(updateUptime, 60000) // 每分钟更新一次

    return () => clearInterval(interval)
  }, [])

  // 导航菜单项
  const navItems: NavItem[] = [
    {
      key: 'dashboard',
      label: '仪表板',
      icon: '📊',
      path: '/admin',
      description: '系统概览和关键指标'
    },
    {
      key: 'products',
      label: '商品管理',
      icon: '📦',
      path: '/products',
      description: '商品信息录入、查询和管理'
    },
    {
      key: 'templates',
      label: '模板管理',
      icon: '📄',
      path: '/templates',
      description: 'Excel模板上传和批量配置'
    },
    {
      key: 'export',
      label: '批量导出',
      icon: '📤',
      path: '/batch/export',
      description: '商品筛选和批量导出功能'
    },
    {
      key: 'tasks',
      label: '任务管理',
      icon: '📋',
      path: '/admin/tasks',
      description: '查看和管理所有任务'
    },
    {
      key: 'monitor',
      label: '系统监控',
      icon: '🖥️',
      path: '/admin/monitor',
      description: '系统性能和资源监控'
    },
    {
      key: 'files',
      label: '文件管理',
      icon: '📁',
      path: '/admin/files',
      description: '上传和输出文件管理'
    },
    {
      key: 'settings',
      label: '系统设置',
      icon: '⚙️',
      path: '/admin/settings',
      description: '系统配置和参数'
    }
  ]

  // 获取当前活跃的导航项
  const getActiveNavItem = () => {
    return navItems.find(item => pathname === item.path || pathname.startsWith(item.path + '/'))
  }

  const activeItem = getActiveNavItem()

  // 处理导航
  const handleNavigation = (path: string) => {
    router.push(path)
  }

  // 获取未读通知数量
  const unreadNotifications = notifications.filter(n => !n.read).length

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* 顶部导航栏 */}
      <header className="bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className="p-2 rounded-md hover:bg-blue-500 text-white transition-colors"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
              <div>
                <h1 className="text-xl font-semibold text-white">
                  🛒 猫超活动工具 - 管理后台
                </h1>
                {activeItem && (
                  <p className="text-sm text-blue-100">{activeItem.description}</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* 通知 */}
              <div className="relative">
                <button
                  onClick={() => {/* 打开通知面板 */}}
                  className="p-2 rounded-md hover:bg-blue-500 text-white transition-colors relative"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84L10.17 8.96M2.82 10.07l3.12 3.12M5.84 7.05L8.96 10.17" />
                  </svg>
                  {unreadNotifications > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {unreadNotifications > 9 ? '9+' : unreadNotifications}
                    </span>
                  )}
                </button>
              </div>

              {/* 返回主应用 */}
              <Button
                variant="outline"
                onClick={() => router.push('/')}
                className="bg-white text-blue-600 border-white hover:bg-blue-50"
              >
                🏠 返回主应用
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className={`bg-white shadow-xl border-r border-gray-200 transition-all duration-300 ${
          sidebarCollapsed ? 'w-16' : 'w-72'
        }`}>
          {/* 侧边栏头部 */}
          <div className="p-6 border-b border-gray-200">
            {!sidebarCollapsed && (
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">🛒</span>
                </div>
                <div>
                  <h2 className="font-bold text-gray-900">猫超活动工具</h2>
                  <p className="text-xs text-gray-500">管理后台</p>
                </div>
              </div>
            )}
          </div>

          <nav className="p-4 space-y-2">
            {navItems.map((item) => {
              const isActive = pathname === item.path || pathname.startsWith(item.path + '/')

              return (
                <button
                  key={item.key}
                  onClick={() => handleNavigation(item.path)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 group ${
                    isActive
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-[1.02]'
                      : 'text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-600 hover:shadow-md hover:transform hover:scale-[1.01]'
                  }`}
                  title={sidebarCollapsed ? item.label : ''}
                >
                  <span className={`text-xl flex-shrink-0 transition-transform duration-200 ${
                    isActive ? '' : 'group-hover:scale-110'
                  }`}>{item.icon}</span>
                  {!sidebarCollapsed && (
                    <>
                      <div className="flex-1">
                        <span className="font-medium block">{item.label}</span>
                        <span className={`text-xs ${
                          isActive ? 'text-blue-100' : 'text-gray-500 group-hover:text-blue-400'
                        }`}>{item.description}</span>
                      </div>
                      {isActive && (
                        <span className="ml-auto">
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </span>
                      )}
                    </>
                  )}
                </button>
              )
            })}
          </nav>

          {/* 侧边栏底部信息 */}
          {!sidebarCollapsed && (
            <div className="absolute bottom-4 left-4 right-4">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-gray-700">系统运行正常</span>
                </div>
                <div className="text-xs text-gray-500 space-y-1">
                  <div>版本: v1.0.0</div>
                  <div>运行时间: {mounted ? `${uptime}分钟` : '加载中...'}</div>
                </div>
              </div>
            </div>
          )}
        </aside>

        {/* 主内容区域 */}
        <main className="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-gray-100">
          <div className="p-8">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </div>
        </main>
      </div>

      {/* 通知面板 */}
      {notifications.length > 0 && (
        <div className="fixed top-20 right-6 w-80 max-h-96 overflow-y-auto bg-white rounded-lg shadow-lg border z-50">
          <div className="p-4 border-b flex justify-between items-center">
            <h3 className="font-medium">通知</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={clearNotifications}
            >
              清空
            </Button>
          </div>
          <div className="divide-y">
            {notifications.slice(0, 10).map((notification, index) => (
              <div key={index} className="p-4">
                <div className={`flex items-start space-x-2 ${
                  notification.type === 'error' ? 'text-red-600' :
                  notification.type === 'warning' ? 'text-yellow-600' :
                  notification.type === 'success' ? 'text-green-600' :
                  'text-blue-600'
                }`}>
                  <div className="flex-shrink-0 mt-0.5">
                    {notification.type === 'error' ? '❌' :
                     notification.type === 'warning' ? '⚠️' :
                     notification.type === 'success' ? '✅' : 'ℹ️'}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{notification.title}</div>
                    <div className="text-sm text-gray-600 mt-1">{notification.message}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(notification.timestamp).toLocaleString('zh-CN')}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}