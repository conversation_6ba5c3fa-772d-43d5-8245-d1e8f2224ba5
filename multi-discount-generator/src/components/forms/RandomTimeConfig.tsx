'use client'

import { useState } from 'react'
import { format, addHours, addMinutes } from 'date-fns'
import { DateTimePicker, Input, Select, type SelectOption } from '@/components/ui'
import { DEFAULT_VALUES, TIME_CONSTANTS } from '@/constants'
import type { RandomConfig } from '@/types'

interface RandomTimeConfigProps {
  value: Partial<RandomConfig>
  onChange: (config: Partial<RandomConfig>) => void
  errors?: {
    startTime?: string
    slotCount?: string
    slotDuration?: string
  }
}

export function RandomTimeConfig({ 
  value, 
  onChange, 
  errors = {} 
}: RandomTimeConfigProps) {
  const [previewSlots, setPreviewSlots] = useState<Array<{start: Date, end: Date}>>([])

  // 时长选项
  const durationOptions: SelectOption[] = [
    { value: 1, label: '1小时' },
    { value: 2, label: '2小时' }
  ]

  const handleStartTimeChange = (date: Date | null) => {
    onChange({
      ...value,
      startTime: date || undefined
    })
    generatePreview({ ...value, startTime: date || undefined })
  }

  const handleSlotCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const count = parseInt(e.target.value)
    const newValue = { ...value, slotCount: isNaN(count) ? undefined : count }
    onChange(newValue)
    generatePreview(newValue)
  }

  const handleSlotDurationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const duration = parseInt(e.target.value) as 1 | 2
    const newValue = { ...value, slotDuration: duration }
    onChange(newValue)
    generatePreview(newValue)
  }

  // 生成预览时间段
  const generatePreview = (config: Partial<RandomConfig>) => {
    if (!config.startTime || !config.slotCount || !config.slotDuration) {
      setPreviewSlots([])
      return
    }

    const slots: Array<{start: Date, end: Date}> = []
    let currentEnd = config.startTime

    for (let i = 0; i < Math.min(config.slotCount, 5); i++) { // 最多预览5个
      // 随机间隔（简化版，实际使用时会更复杂）
      const randomGap = Math.floor(
        Math.random() * 
        (TIME_CONSTANTS.MAX_RANDOM_GAP_MINUTES - TIME_CONSTANTS.MIN_RANDOM_GAP_MINUTES + 1)
      ) + TIME_CONSTANTS.MIN_RANDOM_GAP_MINUTES

      const slotStart = addMinutes(currentEnd, randomGap)
      const slotEnd = addHours(slotStart, config.slotDuration)

      slots.push({ start: slotStart, end: slotEnd })
      currentEnd = slotEnd
    }

    setPreviewSlots(slots)
  }

  return (
    <div className="space-y-6">
      {/* 起始时间 */}
      <DateTimePicker
        label="起始时间"
        value={value.startTime}
        onChange={handleStartTimeChange}
        showTime
        error={errors.startTime}
        helperText="第一个时间段的开始时间"
      />

      {/* 时间段配置 */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <Input
          label="时间段数量"
          type="number"
          min="1"
          max="50"
          value={value.slotCount || DEFAULT_VALUES.TIME_SLOT_COUNT}
          onChange={handleSlotCountChange}
          error={errors.slotCount}
          helperText="要生成的随机时间段数量"
        />

        <Select
          label="每段时长"
          value={value.slotDuration || DEFAULT_VALUES.SLOT_DURATION}
          onChange={handleSlotDurationChange}
          options={durationOptions}
          error={errors.slotDuration}
          helperText="每个时间段的持续时长"
        />
      </div>

      {/* 随机规则说明 */}
      <div className="rounded-md bg-purple-50 border border-purple-200 p-4">
        <h4 className="text-sm font-medium text-purple-900 mb-2">
          🎲 随机生成规则
        </h4>
        <ul className="text-sm text-purple-700 space-y-1">
          <li>• 每个时间段之间随机间隔{TIME_CONSTANTS.MIN_RANDOM_GAP_MINUTES}-{TIME_CONSTANTS.MAX_RANDOM_GAP_MINUTES}分钟</li>
          <li>• 确保所有时间段不重叠且按时间顺序排列</li>
          <li>• 每次生成的时间段都是随机的，适合灵活的活动安排</li>
        </ul>
      </div>

      {/* 预览时间段 */}
      {previewSlots.length > 0 && (
        <div className="rounded-md bg-green-50 border border-green-200 p-4">
          <h4 className="text-sm font-medium text-green-900 mb-3">
            📋 时间段预览 {value.slotCount && value.slotCount > 5 && `(显示前5个，共${value.slotCount}个)`}
          </h4>
          <div className="space-y-2">
            {previewSlots.map((slot, index) => (
              <div key={index} className="flex items-center justify-between bg-white rounded p-2 border">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-green-800">
                    第{index + 1}段:
                  </span>
                  <code className="text-sm bg-green-100 px-2 py-1 rounded">
                    {format(slot.start, 'MM-dd HH:mm:ss')} → {format(slot.end, 'MM-dd HH:mm:ss')}
                  </code>
                </div>
                <span className="text-xs text-green-600">
                  {value.slotDuration}小时
                </span>
              </div>
            ))}
          </div>
          
          <div className="mt-3 pt-3 border-t border-green-200">
            <p className="text-sm text-green-700">
              <span className="font-medium">注意：</span>
              这只是预览示例，实际生成时每次的随机间隔都会不同
            </p>
          </div>
        </div>
      )}

      {/* 使用建议 */}
      <div className="rounded-md bg-gray-50 p-3">
        <h5 className="text-sm font-medium text-gray-900 mb-2">
          💡 使用建议
        </h5>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• 随机模式适合需要灵活时间安排的活动</li>
          <li>• 建议时间段数量不超过20个，避免时间过于分散</li>
          <li>• 可以多次生成不同的随机时间组合进行对比</li>
          <li>• 生成后可在预览表格中查看完整的时间安排</li>
        </ul>
      </div>
    </div>
  )
}