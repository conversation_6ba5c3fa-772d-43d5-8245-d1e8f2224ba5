'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { SimpleSelect as Select } from '@/components/ui/SimpleSelect'
import { Label } from '@/components/ui/Label'
import { Plus, Trash2, Save, X } from 'lucide-react'
import type { 
  ProductFormData, 
  SkuFormData, 
  ActivityType, 
  Supplier,
  ACTIVITY_TYPE_OPTIONS 
} from '@/types/product'

interface ProductFormProps {
  initialData?: Partial<ProductFormData>
  onSubmit: (data: ProductFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export function ProductForm({ 
  initialData, 
  onSubmit, 
  onCancel, 
  loading = false 
}: ProductFormProps) {
  const [formData, setFormData] = useState<ProductFormData>({
    productId: '',
    title: '',
    basePrice: 0,
    limitPrice: undefined,
    purchaseDiscount: undefined,
    activityType: 'DISCOUNT' as ActivityType,
    activityStartTime: undefined,
    activityEndTime: undefined,
    supplierId: undefined,
    skus: [{ skuId: '', skuName: '', price: 0, stock: 0, isActive: true }],
    ...initialData
  })

  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 加载供应商列表
  useEffect(() => {
    fetchSuppliers()
  }, [])

  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/suppliers')
      const result = await response.json()
      if (result.success) {
        setSuppliers(result.data)
      }
    } catch (error) {
      console.error('加载供应商失败:', error)
    }
  }

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.productId.trim()) {
      newErrors.productId = '商品ID不能为空'
    } else if (!/^[A-Za-z0-9_-]+$/.test(formData.productId)) {
      newErrors.productId = '商品ID只能包含字母、数字、下划线和横线'
    }

    if (!formData.title.trim()) {
      newErrors.title = '商品标题不能为空'
    }

    if (formData.basePrice <= 0) {
      newErrors.basePrice = '商品定价必须大于0'
    }

    if (formData.limitPrice && formData.limitPrice <= 0) {
      newErrors.limitPrice = '限价必须大于0'
    }

    if (formData.purchaseDiscount && (formData.purchaseDiscount < 0 || formData.purchaseDiscount > 1)) {
      newErrors.purchaseDiscount = '进货折扣必须在0-1之间'
    }

    // 验证SKU
    formData.skus.forEach((sku, index) => {
      if (!sku.skuId.trim()) {
        newErrors[`sku_${index}_skuId`] = 'SKU ID不能为空'
      } else if (!/^[A-Za-z0-9_-]+$/.test(sku.skuId)) {
        newErrors[`sku_${index}_skuId`] = 'SKU ID格式不正确'
      }

      if (sku.price && sku.price < 0) {
        newErrors[`sku_${index}_price`] = 'SKU价格不能为负数'
      }

      if (sku.stock && sku.stock < 0) {
        newErrors[`sku_${index}_stock`] = '库存不能为负数'
      }
    })

    // 检查SKU ID重复
    const skuIds = formData.skus.map(sku => sku.skuId).filter(id => id.trim())
    const duplicateSkuIds = skuIds.filter((id, index) => skuIds.indexOf(id) !== index)
    if (duplicateSkuIds.length > 0) {
      newErrors.skus = `SKU ID重复: ${duplicateSkuIds.join(', ')}`
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('提交失败:', error)
    }
  }

  // 添加SKU
  const addSku = () => {
    setFormData(prev => ({
      ...prev,
      skus: [...prev.skus, { skuId: '', skuName: '', price: 0, stock: 0, isActive: true }]
    }))
  }

  // 删除SKU
  const removeSku = (index: number) => {
    if (formData.skus.length > 1) {
      setFormData(prev => ({
        ...prev,
        skus: prev.skus.filter((_, i) => i !== index)
      }))
    }
  }

  // 更新SKU
  const updateSku = (index: number, field: keyof SkuFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      skus: prev.skus.map((sku, i) => 
        i === index ? { ...sku, [field]: value } : sku
      )
    }))
  }

  const ACTIVITY_TYPE_OPTIONS = [
    { value: 'DISCOUNT', label: '打折' },
    { value: 'DIRECT_REDUCTION', label: '直降' },
    { value: 'INSTANT_REDUCTION', label: '立减' }
  ]

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">基础信息</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="productId">商品ID *</Label>
            <Input
              id="productId"
              value={formData.productId}
              onChange={(e) => setFormData(prev => ({ ...prev, productId: e.target.value }))}
              placeholder="请输入商品ID"
              error={errors.productId}
            />
          </div>

          <div>
            <Label htmlFor="title">商品标题 *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="请输入商品标题"
              error={errors.title}
            />
          </div>

          <div>
            <Label htmlFor="basePrice">商品定价 *</Label>
            <Input
              id="basePrice"
              type="number"
              step="0.01"
              min="0"
              value={formData.basePrice}
              onChange={(e) => setFormData(prev => ({ ...prev, basePrice: parseFloat(e.target.value) || 0 }))}
              placeholder="请输入商品定价"
              error={errors.basePrice}
            />
          </div>

          <div>
            <Label htmlFor="limitPrice">限价价格</Label>
            <Input
              id="limitPrice"
              type="number"
              step="0.01"
              min="0"
              value={formData.limitPrice || ''}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                limitPrice: e.target.value ? parseFloat(e.target.value) : undefined 
              }))}
              placeholder="请输入限价价格"
              error={errors.limitPrice}
            />
          </div>

          <div>
            <Label htmlFor="purchaseDiscount">进货折扣</Label>
            <Input
              id="purchaseDiscount"
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={formData.purchaseDiscount || ''}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                purchaseDiscount: e.target.value ? parseFloat(e.target.value) : undefined 
              }))}
              placeholder="请输入进货折扣 (0-1)"
              error={errors.purchaseDiscount}
            />
          </div>

          <div>
            <Label htmlFor="activityType">活动形式</Label>
            <Select
              value={formData.activityType}
              onValueChange={(value) => setFormData(prev => ({ ...prev, activityType: value as ActivityType }))}
            >
              {ACTIVITY_TYPE_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>
          </div>

          <div>
            <Label htmlFor="supplierId">供应商</Label>
            <Select
              value={formData.supplierId || ''}
              onValueChange={(value) => setFormData(prev => ({ ...prev, supplierId: value || undefined }))}
            >
              <option value="">请选择供应商</option>
              {suppliers.map(supplier => (
                <option key={supplier.id} value={supplier.id}>
                  {supplier.name} ({supplier.code})
                </option>
              ))}
            </Select>
          </div>
        </div>
      </Card>

      {/* SKU信息 */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">SKU信息</h3>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addSku}
          >
            <Plus className="w-4 h-4 mr-2" />
            添加SKU
          </Button>
        </div>

        {errors.skus && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800 text-sm">{errors.skus}</p>
          </div>
        )}

        <div className="space-y-4">
          {formData.skus.map((sku, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-center mb-3">
                <h4 className="font-medium text-gray-700">SKU #{index + 1}</h4>
                {formData.skus.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeSku(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor={`sku_${index}_skuId`}>SKU ID *</Label>
                  <Input
                    id={`sku_${index}_skuId`}
                    value={sku.skuId}
                    onChange={(e) => updateSku(index, 'skuId', e.target.value)}
                    placeholder="请输入SKU ID"
                    error={errors[`sku_${index}_skuId`]}
                  />
                </div>

                <div>
                  <Label htmlFor={`sku_${index}_skuName`}>SKU名称</Label>
                  <Input
                    id={`sku_${index}_skuName`}
                    value={sku.skuName || ''}
                    onChange={(e) => updateSku(index, 'skuName', e.target.value)}
                    placeholder="请输入SKU名称"
                  />
                </div>

                <div>
                  <Label htmlFor={`sku_${index}_price`}>SKU价格</Label>
                  <Input
                    id={`sku_${index}_price`}
                    type="number"
                    step="0.01"
                    min="0"
                    value={sku.price || ''}
                    onChange={(e) => updateSku(index, 'price', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder="请输入SKU价格"
                    error={errors[`sku_${index}_price`]}
                  />
                </div>

                <div>
                  <Label htmlFor={`sku_${index}_stock`}>库存数量</Label>
                  <Input
                    id={`sku_${index}_stock`}
                    type="number"
                    min="0"
                    value={sku.stock || ''}
                    onChange={(e) => updateSku(index, 'stock', e.target.value ? parseInt(e.target.value) : undefined)}
                    placeholder="请输入库存数量"
                    error={errors[`sku_${index}_stock`]}
                  />
                </div>
              </div>

              <div className="mt-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={sku.isActive}
                    onChange={(e) => updateSku(index, 'isActive', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">启用此SKU</span>
                </label>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* 活动时间 */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">活动时间</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="activityStartTime">活动开始时间</Label>
            <Input
              id="activityStartTime"
              type="datetime-local"
              value={formData.activityStartTime ?
                new Date(formData.activityStartTime.getTime() - formData.activityStartTime.getTimezoneOffset() * 60000)
                  .toISOString().slice(0, 16) : ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                activityStartTime: e.target.value ? new Date(e.target.value) : undefined
              }))}
            />
          </div>

          <div>
            <Label htmlFor="activityEndTime">活动结束时间</Label>
            <Input
              id="activityEndTime"
              type="datetime-local"
              value={formData.activityEndTime ?
                new Date(formData.activityEndTime.getTime() - formData.activityEndTime.getTimezoneOffset() * 60000)
                  .toISOString().slice(0, 16) : ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                activityEndTime: e.target.value ? new Date(e.target.value) : undefined
              }))}
            />
          </div>
        </div>
      </Card>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          <X className="w-4 h-4 mr-2" />
          取消
        </Button>
        <Button
          type="submit"
          disabled={loading}
        >
          <Save className="w-4 h-4 mr-2" />
          {loading ? '保存中...' : '保存'}
        </Button>
      </div>
    </form>
  )
}
